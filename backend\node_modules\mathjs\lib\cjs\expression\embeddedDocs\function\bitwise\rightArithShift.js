"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.rightArithShiftDocs = void 0;
var rightArithShiftDocs = exports.rightArithShiftDocs = {
  name: 'rightArithShift',
  category: 'Bitwise',
  syntax: ['x >> y', 'rightArithShift(x, y)'],
  description: 'Bitwise right arithmetic shift of a value x by y number of bits.',
  examples: ['8 >> 1', '4 << 1', '-12 >> 2'],
  seealso: ['bitAnd', 'bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightLogShift']
};