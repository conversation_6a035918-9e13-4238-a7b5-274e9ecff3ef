## Contributing

Contributions to the math.js library are very welcome! We can't do this alone. You can contribute in different ways: spread the word, report bugs, come up with ideas and suggestions, and contribute to the code.

### What can I do?

Search through the [issues](https://github.com/josdejong/mathjs/issues) looking for bugs that you can help fix, or look at [discussions/ideas](https://github.com/josdejong/mathjs/discussions/categories/ideas) to see if there is an idea for a new feature that that looks interesting to pick up.

### There are a few preferences regarding code contributions:

- The code of math.js follows the JavaScript Standard Style as described on https://standardjs.com/, powered by `eslint`. Run `npm run lint` to test, and `npm run format` to automatically adjust your code to the code style.
- Make sure you properly unit test your changes.
- Before creating a pull request, run the unit tests to make sure they all pass.
- Only commit changes done in the source files under `src`, not to the generated builds
  which are located in the folders `dist` and `lib`.
- Send pull requests to the `develop` branch, not the `master` branch.

### FAQ

- How to implement a new function? Answer: See the section [Implementing a new function](README.md#implementing-a-new-function) in the README.md.
- Where to find more documentation? Answer: best starting point is the project's [README.md](https://github.com/josdejong/mathjs#readme) file. 
- I'm not sure how/where to start, what now? Answer: feel free to ask when in doubt, just [open an issue](https://github.com/josdejong/mathjs/issues) and discuss your thoughts and questions.

Thanks!
