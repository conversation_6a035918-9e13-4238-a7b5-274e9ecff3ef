"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "isAccessorNode", {
  enumerable: true,
  get: function get() {
    return _is.isAccessorNode;
  }
});
Object.defineProperty(exports, "isArray", {
  enumerable: true,
  get: function get() {
    return _is.isArray;
  }
});
Object.defineProperty(exports, "isArrayNode", {
  enumerable: true,
  get: function get() {
    return _is.isArrayNode;
  }
});
Object.defineProperty(exports, "isAssignmentNode", {
  enumerable: true,
  get: function get() {
    return _is.isAssignmentNode;
  }
});
Object.defineProperty(exports, "isBigNumber", {
  enumerable: true,
  get: function get() {
    return _is.isBigNumber;
  }
});
Object.defineProperty(exports, "isBlockNode", {
  enumerable: true,
  get: function get() {
    return _is.isBlockNode;
  }
});
Object.defineProperty(exports, "isBoolean", {
  enumerable: true,
  get: function get() {
    return _is.isBoolean;
  }
});
Object.defineProperty(exports, "isChain", {
  enumerable: true,
  get: function get() {
    return _is.isChain;
  }
});
Object.defineProperty(exports, "isCollection", {
  enumerable: true,
  get: function get() {
    return _is.isCollection;
  }
});
Object.defineProperty(exports, "isComplex", {
  enumerable: true,
  get: function get() {
    return _is.isComplex;
  }
});
Object.defineProperty(exports, "isConditionalNode", {
  enumerable: true,
  get: function get() {
    return _is.isConditionalNode;
  }
});
Object.defineProperty(exports, "isConstantNode", {
  enumerable: true,
  get: function get() {
    return _is.isConstantNode;
  }
});
Object.defineProperty(exports, "isDate", {
  enumerable: true,
  get: function get() {
    return _is.isDate;
  }
});
Object.defineProperty(exports, "isDenseMatrix", {
  enumerable: true,
  get: function get() {
    return _is.isDenseMatrix;
  }
});
Object.defineProperty(exports, "isFraction", {
  enumerable: true,
  get: function get() {
    return _is.isFraction;
  }
});
Object.defineProperty(exports, "isFunction", {
  enumerable: true,
  get: function get() {
    return _is.isFunction;
  }
});
Object.defineProperty(exports, "isFunctionAssignmentNode", {
  enumerable: true,
  get: function get() {
    return _is.isFunctionAssignmentNode;
  }
});
Object.defineProperty(exports, "isFunctionNode", {
  enumerable: true,
  get: function get() {
    return _is.isFunctionNode;
  }
});
Object.defineProperty(exports, "isHelp", {
  enumerable: true,
  get: function get() {
    return _is.isHelp;
  }
});
Object.defineProperty(exports, "isIndex", {
  enumerable: true,
  get: function get() {
    return _is.isIndex;
  }
});
Object.defineProperty(exports, "isIndexNode", {
  enumerable: true,
  get: function get() {
    return _is.isIndexNode;
  }
});
Object.defineProperty(exports, "isMatrix", {
  enumerable: true,
  get: function get() {
    return _is.isMatrix;
  }
});
Object.defineProperty(exports, "isNode", {
  enumerable: true,
  get: function get() {
    return _is.isNode;
  }
});
Object.defineProperty(exports, "isNull", {
  enumerable: true,
  get: function get() {
    return _is.isNull;
  }
});
Object.defineProperty(exports, "isNumber", {
  enumerable: true,
  get: function get() {
    return _is.isNumber;
  }
});
Object.defineProperty(exports, "isObject", {
  enumerable: true,
  get: function get() {
    return _is.isObject;
  }
});
Object.defineProperty(exports, "isObjectNode", {
  enumerable: true,
  get: function get() {
    return _is.isObjectNode;
  }
});
Object.defineProperty(exports, "isOperatorNode", {
  enumerable: true,
  get: function get() {
    return _is.isOperatorNode;
  }
});
Object.defineProperty(exports, "isParenthesisNode", {
  enumerable: true,
  get: function get() {
    return _is.isParenthesisNode;
  }
});
Object.defineProperty(exports, "isRange", {
  enumerable: true,
  get: function get() {
    return _is.isRange;
  }
});
Object.defineProperty(exports, "isRangeNode", {
  enumerable: true,
  get: function get() {
    return _is.isRangeNode;
  }
});
Object.defineProperty(exports, "isRegExp", {
  enumerable: true,
  get: function get() {
    return _is.isRegExp;
  }
});
Object.defineProperty(exports, "isRelationalNode", {
  enumerable: true,
  get: function get() {
    return _is.isRelationalNode;
  }
});
Object.defineProperty(exports, "isResultSet", {
  enumerable: true,
  get: function get() {
    return _is.isResultSet;
  }
});
Object.defineProperty(exports, "isSparseMatrix", {
  enumerable: true,
  get: function get() {
    return _is.isSparseMatrix;
  }
});
Object.defineProperty(exports, "isString", {
  enumerable: true,
  get: function get() {
    return _is.isString;
  }
});
Object.defineProperty(exports, "isSymbolNode", {
  enumerable: true,
  get: function get() {
    return _is.isSymbolNode;
  }
});
Object.defineProperty(exports, "isUndefined", {
  enumerable: true,
  get: function get() {
    return _is.isUndefined;
  }
});
Object.defineProperty(exports, "isUnit", {
  enumerable: true,
  get: function get() {
    return _is.isUnit;
  }
});
var _is = require("../utils/is.js");