# 室内建筑设计师物料价格计算软件 - 后端框架设计

## 1. 技术栈

- **运行环境**: Node.js 18+
- **Web框架**: Express.js 4.x
- **数据库**: SQLite3
- **ORM**: None (使用原生SQL)
- **文件处理**: ExcelJS
- **API文档**: Swagger/OpenAPI 3.0
- **测试框架**: Jest + Supertest
- **日志系统**: Winston
- **配置管理**: dotenv

## 2. 项目目录结构

```
backend/
├── src/
│   ├── config/
│   │   ├── database.js
│   │   └── app.js
│   ├── models/
│   │   ├── base.js
│   │   ├── project.js
│   │   ├── material.js
│   │   ├── room.js
│   │   └── quote.js
│   ├── controllers/
│   │   ├── projectController.js
│   │   ├── materialController.js
│   │   ├── roomController.js
│   │   └── quoteController.js
│   ├── services/
│   │   ├── calculationService.js
│   │   ├── excelService.js
│   │   └── validationService.js
│   ├── routes/
│   │   ├── index.js
│   │   ├── projects.js
│   │   ├── materials.js
│   │   ├── rooms.js
│   │   └── quotes.js
│   ├── middleware/
│   │   ├── errorHandler.js
│   │   ├── validation.js
│   │   └── logger.js
│   └── utils/
│       ├── constants.js
│       ├── helpers.js
│       └── dbInit.js
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/
│   └── api.yaml
├── database/
│   └── material_system.db
├── package.json
├── .env
├── .env.example
└── server.js
```

## 3. 核心代码文件详细说明

### 3.1 服务器入口文件

#### server.js

**代码文件功能说明**: 
- 应用程序主入口点
- 初始化Express服务器
- 配置中间件和路由
- 启动HTTP服务监听

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "compression": "^1.7.4",
  "dotenv": "^16.3.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/config/app.js` → `app` (Express应用实例)
- `src/config/database.js` → `initDatabase()` (数据库初始化函数)
- `src/utils/dbInit.js` → `createTables()` (创建数据表函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（入口文件）

### 3.2 配置文件

#### src/config/app.js

**代码文件功能说明**: 
- Express应用配置
- 中间件注册
- 路由配置
- 错误处理配置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "compression": "^1.7.4",
  "swagger-ui-express": "^5.0.0",
  "yamljs": "^0.3.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/routes/index.js` → `router` (主路由实例)
- `src/middleware/errorHandler.js` → `errorHandler` (错误处理中间件)
- `src/middleware/logger.js` → `requestLogger` (请求日志中间件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `app` (Express应用实例)

#### src/config/database.js

**代码文件功能说明**: 
- SQLite数据库连接配置
- 数据库连接池管理
- 数据库初始化函数

**代码文件依赖的需要格外安装的库**: 
```json
{
  "sqlite3": "^5.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `db` (数据库连接实例)
- `initDatabase()` (数据库初始化函数)
- `closeDatabase()` (关闭数据库连接函数)

### 3.3 数据模型层

#### src/models/base.js

**代码文件功能说明**: 
- 基础模型类定义
- 通用CRUD操作方法
- 数据验证基础方法

**代码文件依赖的需要格外安装的库**: 
```json
{
  "sqlite3": "^5.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/config/database.js` → `db` (数据库连接实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `BaseModel` (基础模型类)
- `execute()` (SQL执行方法)
- `findById()` (根据ID查找记录)
- `findAll()` (查找所有记录)
- `create()` (创建记录)
- `update()` (更新记录)
- `delete()` (删除记录)

#### src/models/project.js

**代码文件功能说明**: 
- 项目数据模型
- 项目相关的数据库操作
- 项目统计信息计算

**代码文件依赖的需要格外安装的库**: 
- 无（继承base.js依赖项）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/base.js` → `BaseModel` (基础模型类)
- `src/utils/helpers.js` → `formatDate()` (日期格式化函数), `generateUUID()` (生成UUID函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `ProjectModel` (项目模型类)
- `getProjectList()` (获取项目列表)
- `getProjectById()` (根据ID获取项目详情)
- `createProject()` (创建新项目)
- `updateProject()` (更新项目信息)
- `deleteProject()` (删除项目)
- `getProjectStats()` (获取项目统计信息)

#### src/models/material.js

**代码文件功能说明**: 
- 物料数据模型
- 物料类型、模板、项目物料的数据库操作
- 物料计价方式处理

**代码文件依赖的需要格外安装的库**: 
- 无（继承base.js依赖项）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/base.js` → `BaseModel` (基础模型类)
- `src/utils/constants.js` → `PRICING_METHODS` (计价方式枚举), `UNITS` (单位枚举)
- `src/utils/helpers.js` → `validatePricingMethod()` (验证计价方式函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `MaterialModel` (物料模型类)
- `getMaterialTypes()` (获取物料类型列表)
- `createMaterialType()` (创建物料类型)
- `getMaterialTemplates()` (获取物料模板列表)
- `createMaterialTemplate()` (创建物料模板)
- `getProjectMaterials()` (获取项目物料列表)
- `createProjectMaterial()` (创建项目物料)
- `updateProjectMaterial()` (更新项目物料)
- `deleteProjectMaterial()` (删除项目物料)
- `calculateMaterialUsage()` (计算物料用量)

#### src/models/room.js

**代码文件功能说明**: 
- 房间数据模型
- 房间类型、模板、项目房间的数据库操作
- 房间物料清单管理

**代码文件依赖的需要格外安装的库**: 
- 无（继承base.js依赖项）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/base.js` → `BaseModel` (基础模型类)
- `src/models/material.js` → `MaterialModel` (物料模型类)
- `src/utils/helpers.js` → `calculateFormula()` (公式计算函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `RoomModel` (房间模型类)
- `getRoomTypes()` (获取房间类型列表)
- `createRoomType()` (创建房间类型)
- `getRoomTemplates()` (获取房间模板列表)
- `createRoomTemplate()` (创建房间模板)
- `getProjectRooms()` (获取项目房间列表)
- `createProjectRoom()` (创建项目房间)
- `updateProjectRoom()` (更新项目房间)
- `deleteProjectRoom()` (删除项目房间)
- `getRoomMaterials()` (获取房间物料清单)
- `calculateRoomPrice()` (计算房间总价)

#### src/models/quote.js

**代码文件功能说明**: 
- 报价数据模型
- 报价方案的数据库操作
- 报价计算和生成逻辑

**代码文件依赖的需要格外安装的库**: 
- 无（继承base.js依赖项）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/base.js` → `BaseModel` (基础模型类)
- `src/models/project.js` → `ProjectModel` (项目模型类)
- `src/models/material.js` → `MaterialModel` (物料模型类)
- `src/models/room.js` → `RoomModel` (房间模型类)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `QuoteModel` (报价模型类)
- `getProjectQuotes()` (获取项目报价列表)
- `createQuote()` (创建报价方案)
- `updateQuote()` (更新报价方案)
- `deleteQuote()` (删除报价方案)
- `copyQuote()` (复制报价方案)
- `calculateQuoteDetails()` (计算报价详情)
- `applyDiscount()` (应用折扣系数)

### 3.4 控制器层

#### src/controllers/projectController.js

**代码文件功能说明**: 
- 项目相关HTTP请求处理
- 请求参数验证和响应格式化
- 业务逻辑调用和错误处理

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/project.js` → `ProjectModel` (项目模型类)
- `src/services/validationService.js` → `validateProject()` (验证项目数据函数)
- `src/utils/helpers.js` → `formatResponse()` (响应格式化函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getProjects` (获取项目列表控制器)
- `getProject` (获取单个项目控制器)
- `createProject` (创建项目控制器)
- `updateProject` (更新项目控制器)
- `deleteProject` (删除项目控制器)
- `getProjectStats` (获取项目统计控制器)

#### src/controllers/materialController.js

**代码文件功能说明**: 
- 物料相关HTTP请求处理
- 物料类型、模板、项目物料的API接口
- 物料数据验证和格式化

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/material.js` → `MaterialModel` (物料模型类)
- `src/services/validationService.js` → `validateMaterial()` (验证物料数据函数)
- `src/services/calculationService.js` → `calculateMaterialCost()` (计算物料成本函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getMaterialTypes` (获取物料类型控制器)
- `createMaterialType` (创建物料类型控制器)
- `getMaterialTemplates` (获取物料模板控制器)
- `createMaterialTemplate` (创建物料模板控制器)
- `getProjectMaterials` (获取项目物料控制器)
- `createProjectMaterial` (创建项目物料控制器)
- `updateProjectMaterial` (更新项目物料控制器)
- `deleteProjectMaterial` (删除项目物料控制器)

#### src/controllers/roomController.js

**代码文件功能说明**: 
- 房间相关HTTP请求处理
- 房间类型、模板、项目房间的API接口
- 房间物料清单管理接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/room.js` → `RoomModel` (房间模型类)
- `src/services/validationService.js` → `validateRoom()` (验证房间数据函数)
- `src/services/calculationService.js` → `calculateRoomCost()` (计算房间成本函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getRoomTypes` (获取房间类型控制器)
- `createRoomType` (创建房间类型控制器)
- `getRoomTemplates` (获取房间模板控制器)
- `createRoomTemplate` (创建房间模板控制器)
- `getProjectRooms` (获取项目房间控制器)
- `createProjectRoom` (创建项目房间控制器)
- `updateProjectRoom` (更新项目房间控制器)
- `deleteProjectRoom` (删除项目房间控制器)

#### src/controllers/quoteController.js

**代码文件功能说明**: 
- 报价相关HTTP请求处理
- 报价方案的CRUD操作接口
- Excel导出功能接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/quote.js` → `QuoteModel` (报价模型类)
- `src/services/excelService.js` → `generateQuoteExcel()` (生成报价Excel函数)
- `src/services/calculationService.js` → `calculateQuote()` (计算报价函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getQuotes` (获取报价列表控制器)
- `createQuote` (创建报价控制器)
- `updateQuote` (更新报价控制器)
- `deleteQuote` (删除报价控制器)
- `copyQuote` (复制报价控制器)
- `exportQuoteExcel` (导出Excel控制器)

### 3.5 服务层

#### src/services/calculationService.js

**代码文件功能说明**: 
- 核心计算逻辑服务
- 物料用量计算
- 房间价格计算
- 项目总价计算

**代码文件依赖的需要格外安装的库**: 
```json
{
  "mathjs": "^11.11.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/constants.js` → `PRICING_METHODS` (计价方式枚举), `CALCULATION_FORMULAS` (计算公式常量)
- `src/utils/helpers.js` → `safeEval()` (安全表达式求值函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `calculateMaterialUsage()` (计算物料用量)
- `calculateMaterialCost()` (计算物料成本)
- `calculateRoomCost()` (计算房间成本)
- `calculateProjectTotal()` (计算项目总价)
- `applyFormula()` (应用计算公式)
- `validateCalculationParams()` (验证计算参数)

#### src/services/excelService.js

**代码文件功能说明**: 
- Excel文件生成服务
- 报价表格式化和导出
- 支持多种Excel模板

**代码文件依赖的需要格外安装的库**: 
```json
{
  "exceljs": "^4.4.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/helpers.js` → `formatCurrency()` (货币格式化函数), `formatDate()` (日期格式化函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `generateQuoteExcel()` (生成报价Excel)
- `createWorkbook()` (创建工作簿)
- `formatQuoteSheet()` (格式化报价表)
- `addQuoteData()` (添加报价数据)
- `styleWorksheet()` (设置工作表样式)

#### src/services/validationService.js

**代码文件功能说明**: 
- 数据验证服务
- 业务规则验证
- 输入参数检查

**代码文件依赖的需要格外安装的库**: 
```json
{
  "joi": "^17.9.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/constants.js` → `VALIDATION_RULES` (验证规则配置)
- `src/models/base.js` → `BaseModel` (基础模型类)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `validateProject()` (验证项目数据)
- `validateMaterial()` (验证物料数据)
- `validateRoom()` (验证房间数据)
- `validateQuote()` (验证报价数据)
- `checkNameUnique()` (检查名称唯一性)
- `validatePricingMethod()` (验证计价方式)

### 3.6 路由层

#### src/routes/index.js

**代码文件功能说明**: 
- 主路由文件
- API版本管理
- 路由模块整合

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/routes/projects.js` → `projectRoutes` (项目路由实例)
- `src/routes/materials.js` → `materialRoutes` (物料路由实例)
- `src/routes/rooms.js` → `roomRoutes` (房间路由实例)
- `src/routes/quotes.js` → `quoteRoutes` (报价路由实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (主路由实例)

#### src/routes/projects.js

**代码文件功能说明**: 
- 项目模块路由定义
- RESTful API路径映射
- 中间件绑定

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/controllers/projectController.js` → `getProjects` (获取项目列表控制器), `getProject` (获取单个项目控制器), `createProject` (创建项目控制器), `updateProject` (更新项目控制器), `deleteProject` (删除项目控制器), `getProjectStats` (获取项目统计控制器)
- `src/middleware/validation.js` → `validateRequest` (请求验证中间件工厂)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (项目路由实例)

#### src/routes/materials.js

**代码文件功能说明**: 
- 物料模块路由定义
- 支持物料类型、模板、项目物料的路由
- 参数验证中间件配置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/controllers/materialController.js` → `getMaterialTypes` (获取物料类型控制器), `createMaterialType` (创建物料类型控制器), `getMaterialTemplates` (获取物料模板控制器), `createMaterialTemplate` (创建物料模板控制器), `getProjectMaterials` (获取项目物料控制器), `createProjectMaterial` (创建项目物料控制器), `updateProjectMaterial` (更新项目物料控制器), `deleteProjectMaterial` (删除项目物料控制器)
- `src/middleware/validation.js` → `validateRequest` (请求验证中间件工厂)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (物料路由实例)

#### src/routes/rooms.js

**代码文件功能说明**: 
- 房间模块路由定义
- 房间类型、模板、项目房间的API路径
- 房间物料关联路由

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/controllers/roomController.js` → `getRoomTypes` (获取房间类型控制器), `createRoomType` (创建房间类型控制器), `getRoomTemplates` (获取房间模板控制器), `createRoomTemplate` (创建房间模板控制器), `getProjectRooms` (获取项目房间控制器), `createProjectRoom` (创建项目房间控制器), `updateProjectRoom` (更新项目房间控制器), `deleteProjectRoom` (删除项目房间控制器)
- `src/middleware/validation.js` → `validateRequest` (请求验证中间件工厂)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (房间路由实例)

#### src/routes/quotes.js

**代码文件功能说明**: 
- 报价模块路由定义
- 报价CRUD操作路由
- Excel导出路由配置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "express": "^4.18.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/controllers/quoteController.js` → `getQuotes` (获取报价列表控制器), `createQuote` (创建报价控制器), `updateQuote` (更新报价控制器), `deleteQuote` (删除报价控制器), `copyQuote` (复制报价控制器), `exportQuoteExcel` (导出Excel控制器)
- `src/middleware/validation.js` → `validateRequest` (请求验证中间件工厂)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (报价路由实例)

### 3.7 中间件层

#### src/middleware/errorHandler.js

**代码文件功能说明**: 
- 全局错误处理中间件
- 错误信息格式化
- 日志记录和响应处理

**代码文件依赖的需要格外安装的库**: 
```json
{
  "winston": "^3.10.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/helpers.js` → `formatErrorResponse()` (错误响应格式化函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `errorHandler` (错误处理中间件)
- `notFoundHandler` (404处理中间件)
- `ValidationError` (验证错误类)
- `BusinessError` (业务错误类)

#### src/middleware/validation.js

**代码文件功能说明**: 
- 请求参数验证中间件
- 基于Joi的数据验证
- 自定义验证规则支持

**代码文件依赖的需要格外安装的库**: 
```json
{
  "joi": "^17.9.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/services/validationService.js` → `validateProject()` (验证项目数据函数), `validateMaterial()` (验证物料数据函数), `validateRoom()` (验证房间数据函数), `validateQuote()` (验证报价数据函数), `checkNameUnique()` (检查名称唯一性函数), `validatePricingMethod()` (验证计价方式函数)
- `src/middleware/errorHandler.js` → `ValidationError` (验证错误类)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `validateRequest` (请求验证中间件工厂)
- `validateParams` (路径参数验证)
- `validateBody` (请求体验证)
- `validateQuery` (查询参数验证)

#### src/middleware/logger.js

**代码文件功能说明**: 
- HTTP请求日志中间件
- 性能监控和访问记录
- 结构化日志输出

**代码文件依赖的需要格外安装的库**: 
```json
{
  "winston": "^3.10.0",
  "morgan": "^1.10.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `requestLogger` (请求日志中间件)
- `logger` (Winston日志实例)
- `logStream` (日志流对象)

### 3.8 工具类

#### src/utils/constants.js

**代码文件功能说明**: 
- 应用常量定义
- 枚举值和配置项
- 业务规则常量

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `PRICING_METHODS` (计价方式枚举)
- `UNITS` (单位枚举)
- `ROOM_TYPES` (房间类型枚举)
- `QUOTE_STATUS` (报价状态枚举)
- `ERROR_CODES` (错误代码枚举)
- `VALIDATION_RULES` (验证规则配置)
- `DEFAULT_CONFIG` (默认配置)

#### src/utils/helpers.js

**代码文件功能说明**: 
- 通用辅助函数库
- 数据格式化和转换
- 常用算法和工具函数

**代码文件依赖的需要格外安装的库**: 
```json
{
  "uuid": "^9.0.0",
  "moment": "^2.29.4"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `generateUUID()` (生成UUID)
- `formatDate()` (日期格式化)
- `formatCurrency()` (货币格式化)
- `formatResponse()` (响应格式化)
- `formatErrorResponse()` (错误响应格式化)
- `safeEval()` (安全表达式求值)
- `deepClone()` (深拷贝对象)
- `isEmpty()` (判断空值)
- `calculateFormula()` (公式计算)

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/config/database.js` → `db` (数据库连接实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `createTables()` (创建数据表)
- `insertInitialData()` (插入初始数据)
- `dropTables()` (删除数据表)
- `resetDatabase()` (重置数据库)

## 4. API接口设计

### 4.1 项目管理接口

```yaml
# 获取项目列表
GET /api/v1/projects
# 获取单个项目
GET /api/v1/projects/:id
# 创建项目
POST /api/v1/projects
# 更新项目
PUT /api/v1/projects/:id
# 删除项目
DELETE /api/v1/projects/:id
# 获取项目统计
GET /api/v1/projects/:id/stats
```

### 4.2 物料管理接口

```yaml
# 物料类型
GET /api/v1/materials/types
POST /api/v1/materials/types
PUT /api/v1/materials/types/:id
DELETE /api/v1/materials/types/:id

# 物料模板
GET /api/v1/materials/templates
POST /api/v1/materials/templates
PUT /api/v1/materials/templates/:id
DELETE /api/v1/materials/templates/:id

# 项目物料
GET /api/v1/projects/:projectId/materials
POST /api/v1/projects/:projectId/materials
PUT /api/v1/projects/:projectId/materials/:id
DELETE /api/v1/projects/:projectId/materials/:id
```

### 4.3 房间管理接口

```yaml
# 房间类型
GET /api/v1/rooms/types
POST /api/v1/rooms/types
PUT /api/v1/rooms/types/:id
DELETE /api/v1/rooms/types/:id

# 房间模板
GET /api/v1/rooms/templates
POST /api/v1/rooms/templates
PUT /api/v1/rooms/templates/:id
DELETE /api/v1/rooms/templates/:id

# 项目房间
GET /api/v1/projects/:projectId/rooms
POST /api/v1/projects/:projectId/rooms
PUT /api/v1/projects/:projectId/rooms/:id
DELETE /api/v1/projects/:projectId/rooms/:id
```

### 4.4 报价管理接口

```yaml
# 获取项目报价列表
GET /api/v1/projects/:projectId/quotes
# 创建报价
POST /api/v1/projects/:projectId/quotes
# 更新报价
PUT /api/v1/projects/:projectId/quotes/:id
# 删除报价
DELETE /api/v1/projects/:projectId/quotes/:id
# 复制报价
POST /api/v1/projects/:projectId/quotes/:id/copy
# 导出Excel
GET /api/v1/projects/:projectId/quotes/:id/export
```

## 5. 测试模块设计

### 5.1 测试目录结构

```
tests/
├── unit/
│   ├── models/
│   │   ├── project.test.js
│   │   ├── material.test.js
│   │   ├── room.test.js
│   │   └── quote.test.js
│   ├── services/
│   │   ├── calculationService.test.js
│   │   ├── excelService.test.js
│   │   └── validationService.test.js
│   └── utils/
│       └── helpers.test.js
├── integration/
│   ├── projects.test.js
│   ├── materials.test.js
│   ├── rooms.test.js
│   └── quotes.test.js
└── fixtures/
    ├── projects.json
    ├── materials.json
    ├── rooms.json
    └── quotes.json
```

### 5.2 核心测试文件

#### tests/unit/models/project.test.js

**代码文件功能说明**: 
- 项目模型单元测试
- 数据库操作测试
- 业务逻辑验证

**代码文件依赖的需要格外安装的库**: 
```json
{
  "jest": "^29.6.2",
  "sqlite3": "^5.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/models/project.js` → `ProjectModel` (项目模型类)
- `tests/fixtures/projects.json` → `mockProjects` (模拟项目数据), `testProjectData` (测试项目数据)
- `src/config/database.js` → `db` (数据库连接实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（测试文件）

#### tests/integration/projects.test.js

**代码文件功能说明**: 
- 项目接口集成测试
- API端到端测试
- 请求响应验证

**代码文件依赖的需要格外安装的库**: 
```json
{
  "jest": "^29.6.2",
  "supertest": "^6.3.3"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/config/app.js` → `app` (Express应用实例)
- `tests/fixtures/projects.json` → `mockProjects` (模拟项目数据), `testProjectData` (测试项目数据)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（测试文件）

#### tests/unit/services/calculationService.test.js

**代码文件功能说明**: 
- 计算服务单元测试
- 数学公式验证
- 边界条件测试

**代码文件依赖的需要格外安装的库**: 
```json
{
  "jest": "^29.6.2"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/services/calculationService.js` → `calculateMaterialUsage()` (计算物料用量函数), `calculateMaterialCost()` (计算物料成本函数), `calculateRoomCost()` (计算房间成本函数), `calculateProjectTotal()` (计算项目总价函数), `applyFormula()` (应用计算公式函数), `validateCalculationParams()` (验证计算参数函数)
- `tests/fixtures/materials.json` → 测试物料数据

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（测试文件）

#### tests/fixtures/projects.json

**代码文件功能说明**: 
- 项目测试数据定义
- 模拟真实业务场景
- 支持多种测试用例

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `mockProjects` (模拟项目数据)
- `testProjectData` (测试项目数据)

## 6. 数据库设计

### 6.1 数据表结构

```sql
-- 项目表
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 物料类型表
CREATE TABLE material_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    pricing_method VARCHAR(50) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 物料模板表
CREATE TABLE material_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    type_id INTEGER NOT NULL,
    default_price DECIMAL(10,2) DEFAULT 0,
    parameters TEXT, -- JSON格式存储参数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (type_id) REFERENCES material_types(id)
);

-- 项目物料表
CREATE TABLE project_materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    type_id INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    parameters TEXT, -- JSON格式存储参数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES material_types(id),
    UNIQUE(project_id, name)
);

-- 房间类型表
CREATE TABLE room_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 房间模板表
CREATE TABLE room_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    type_id INTEGER NOT NULL,
    materials TEXT, -- JSON格式存储物料清单
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (type_id) REFERENCES room_types(id)
);

-- 项目房间表
CREATE TABLE project_rooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    type_id INTEGER NOT NULL,
    quantity INTEGER DEFAULT 1,
    materials TEXT, -- JSON格式存储物料清单
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES room_types(id)
);

-- 报价表
CREATE TABLE quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    discount_rate DECIMAL(5,4) DEFAULT 1.0000,
    total_amount DECIMAL(12,2) DEFAULT 0,
    details TEXT, -- JSON格式存储报价详情
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE(project_id, name)
);
```

## 7. 环境配置文件

### 7.1 package.json

```json
{
  "name": "material-system-backend",
  "version": "1.0.0",
  "description": "Interior Design Material Price Calculation System Backend",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:init": "node src/utils/dbInit.js",
    "db:reset": "node src/utils/dbInit.js --reset"
  },
  "dependencies": {
    "express": "^4.18.2",
    "sqlite3": "^5.1.6",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "compression": "^1.7.4",
    "dotenv": "^16.3.1",
    "exceljs": "^4.4.0",
    "joi": "^17.9.2",
    "mathjs": "^11.11.0",
    "winston": "^3.10.0",
    "morgan": "^1.10.0",
    "uuid": "^9.0.0",
    "moment": "^2.29.4",
    "swagger-ui-express": "^5.0.0",
    "yamljs": "^0.3.0"
  },
  "devDependencies": {
    "jest": "^29.6.2",
    "supertest": "^6.3.3",
    "nodemon": "^3.0.1"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
```

### 7.2 .env.example

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_PATH=./database/material_system.db

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Excel导出配置
EXPORT_PATH=./exports

# API配置
API_VERSION=v1
API_PREFIX=/api
```

## 8. 部署说明

### 8.1 开发环境启动

```bash
# 安装依赖
npm install

# 初始化数据库
npm run db:init

# 启动开发服务器
npm run dev
```

### 8.2 生产环境部署

```bash
# 设置环境变量
export NODE_ENV=production

# 启动服务
npm start
```

### 8.3 测试执行

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```
