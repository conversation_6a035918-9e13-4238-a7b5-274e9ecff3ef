<template>
  <div class="home">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <span>欢迎使用物料价格计算系统</span>
        </div>
      </template>
      <div class="welcome-content">
        <el-icon size="64" color="#409eff">
          <House />
        </el-icon>
        <p>系统已成功启动！</p>
        <p>这是一个专为室内建筑设计师打造的物料价格计算软件</p>
        <el-button type="primary" @click="handleStart">
          开始使用
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { House } from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    House
  },
  methods: {
    handleStart() {
      this.$message.success('环境安装成功！')
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.welcome-card {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-content {
  text-align: center;
  padding: 20px;
}

.welcome-content p {
  margin: 10px 0;
  font-size: 16px;
}
</style>
