# 环境安装.md

本项目分为前端与后端两部分，需分别安装依赖。

---

## 一、前端依赖安装

请在 `frontend/` 目录下执行：

```bash
npm install vue@^3.3.4 vue-router@^4.2.4 pinia@^2.1.6 element-plus@^2.3.8 @element-plus/icons-vue@^2.1.0 axios@^1.4.0 xlsx@^0.18.5 dayjs@^1.11.9 lodash@^4.17.21 vite@^4.4.5 @vitejs/plugin-vue@^4.2.3 sass@^1.64.1 vitest@^0.34.1 @vitest/ui@^0.34.1 @vue/test-utils@^2.4.1 eslint@^8.45.0 eslint-plugin-vue@^9.15.1 prettier@^3.0.0 @vitest/coverage-c8@^0.33.0
```

---

## 二、后端依赖安装

请在 `backend/` 目录下执行：

```bash
npm install express@^4.18.2 sqlite3@^5.1.6 cors@^2.8.5 helmet@^7.0.0 compression@^1.7.4 dotenv@^16.3.1 exceljs@^4.4.0 joi@^17.9.2 mathjs@^11.11.0 winston@^3.10.0 morgan@^1.10.0 uuid@^9.0.0 moment@^2.29.4 swagger-ui-express@^5.0.0 yamljs@^0.3.0
```

如需开发和测试，还需安装：

```bash
npm install --save-dev jest@^29.6.2 supertest@^6.3.3 nodemon@^3.0.1
```

---

请根据实际目录结构在对应目录下执行上述命令。
