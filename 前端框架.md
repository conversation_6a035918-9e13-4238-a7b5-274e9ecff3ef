# 室内建筑设计师物料价格计算软件 - 前端框架设计

## 1. 技术栈

- **前端框架**: Vue.js 3.3+
- **构建工具**: Vite 4.x
- **UI组件库**: Element Plus 2.3+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.x
- **HTTP客户端**: Axios 1.4+
- **Excel处理**: xlsx 0.18+
- **图标库**: @element-plus/icons-vue
- **CSS预处理器**: Sass/SCSS
- **工具库**: Lodash 4.x
- **时间处理**: Day.js 1.11+

## 2. 项目目录结构

```
frontend/
├── public/
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/
│   │   ├── index.js
│   │   ├── projects.js
│   │   ├── materials.js
│   │   ├── rooms.js
│   │   └── quotes.js
│   ├── assets/
│   │   ├── styles/
│   │   │   ├── main.scss
│   │   │   ├── variables.scss
│   │   │   └── components.scss
│   │   ├── images/
│   │   └── icons/
│   ├── components/
│   │   ├── common/
│   │   │   ├── BaseDialog.vue
│   │   │   ├── BaseButton.vue
│   │   │   ├── BaseCard.vue
│   │   │   ├── BaseTable.vue
│   │   │   ├── BaseForm.vue
│   │   │   ├── ConfirmDialog.vue
│   │   │   └── LoadingSpinner.vue
│   │   ├── layout/
│   │   │   ├── AppHeader.vue
│   │   │   ├── AppSidebar.vue
│   │   │   ├── MainLayout.vue
│   │   │   └── BreadcrumbNav.vue
│   │   ├── project/
│   │   │   ├── ProjectCard.vue
│   │   │   ├── ProjectList.vue
│   │   │   ├── ProjectForm.vue
│   │   │   ├── ProjectStats.vue
│   │   │   └── ProjectWorkspace.vue
│   │   ├── material/
│   │   │   ├── MaterialIcon.vue
│   │   │   ├── MaterialList.vue
│   │   │   ├── MaterialForm.vue
│   │   │   ├── MaterialTypeSelector.vue
│   │   │   ├── MaterialTemplateManager.vue
│   │   │   └── PricingMethodEditor.vue
│   │   ├── room/
│   │   │   ├── RoomIcon.vue
│   │   │   ├── RoomList.vue
│   │   │   ├── RoomForm.vue
│   │   │   ├── RoomTemplateManager.vue
│   │   │   ├── RoomMaterialEditor.vue
│   │   │   └── FormulaCalculator.vue
│   │   └── quote/
│   │       ├── QuoteCard.vue
│   │       ├── QuoteList.vue
│   │       ├── QuoteForm.vue
│   │       ├── QuoteDetailView.vue
│   │       ├── DiscountEditor.vue
│   │       └── ExcelExporter.vue
│   ├── composables/
│   │   ├── useApi.js
│   │   ├── useConfirm.js
│   │   ├── useLoading.js
│   │   ├── useValidation.js
│   │   ├── useCalculation.js
│   │   ├── useLocalStorage.js
│   │   └── useFormValidation.js
│   ├── stores/
│   │   ├── index.js
│   │   ├── app.js
│   │   ├── project.js
│   │   ├── material.js
│   │   ├── room.js
│   │   └── quote.js
│   ├── utils/
│   │   ├── constants.js
│   │   ├── helpers.js
│   │   ├── formatters.js
│   │   ├── validators.js
│   │   ├── excel.js
│   │   └── storage.js
│   ├── views/
│   │   ├── Home.vue
│   │   ├── ProjectManagement.vue
│   │   ├── ProjectDetail.vue
│   │   ├── MaterialTemplate.vue
│   │   ├── RoomTemplate.vue
│   │   └── QuoteDetail.vue
│   ├── router/
│   │   └── index.js
│   ├── App.vue
│   └── main.js
├── tests/
│   ├── unit/
│   ├── e2e/
│   └── fixtures/
├── package.json
├── vite.config.js
├── vitest.config.js
├── .env.development
├── .env.production
└── README.md
```

## 3. 核心代码文件详细说明

### 3.1 应用入口和配置

#### src/main.js

**代码文件功能说明**: 
- Vue应用主入口文件
- 全局配置和插件注册
- 应用启动和挂载

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue": "^3.3.4",
  "element-plus": "^2.3.8",
  "pinia": "^2.1.6",
  "@element-plus/icons-vue": "^2.1.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/App.vue` → App (根组件)
- `src/router/index.js` → router (路由实例)
- `src/stores/index.js` → pinia (Pinia实例)
- `src/assets/styles/main.scss` → 全局样式文件

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `app` (Vue应用实例)

#### src/App.vue

**代码文件功能说明**: 
- 应用根组件
- 全局布局容器
- 路由视图渲染

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/layout/MainLayout.vue` → MainLayout (主布局组件)
- `src/stores/app.js` → useAppStore (应用状态Store函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（根组件）

#### vite.config.js

**代码文件功能说明**: 
- Vite构建工具配置
- 开发服务器配置
- 插件和别名设置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vite": "^4.4.5",
  "@vitejs/plugin-vue": "^4.2.3",
  "sass": "^1.64.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `config` (Vite配置对象)

### 3.2 路由配置

#### src/router/index.js

**代码文件功能说明**: 
- Vue Router路由配置
- 页面路由定义
- 路由守卫设置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue-router": "^4.2.4"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/views/Home.vue` → Home (首页组件)
- `src/views/ProjectManagement.vue` → ProjectManagement (项目管理组件)
- `src/views/ProjectDetail.vue` → ProjectDetail (项目详情组件)
- `src/views/MaterialTemplate.vue` → MaterialTemplate (物料模板组件)
- `src/views/RoomTemplate.vue` → RoomTemplate (房间模板组件)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `router` (路由实例)
- `routes` (路由配置数组)

### 3.3 状态管理

#### src/stores/index.js

**代码文件功能说明**: 
- Pinia状态管理配置
- Store实例创建
- 全局状态初始化

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `pinia` (Pinia实例)

#### src/stores/app.js

**代码文件功能说明**: 
- 应用全局状态管理
- 侧边栏状态控制
- 主题和语言设置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/storage.js` → getItem() (获取存储项函数), setItem() (设置存储项函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useAppStore` (应用状态Store)
- `sidebarCollapsed` (侧边栏折叠状态)
- `currentProject` (当前选中项目)
- `toggleSidebar()` (切换侧边栏)
- `setCurrentProject()` (设置当前项目)

#### src/stores/project.js

**代码文件功能说明**: 
- 项目数据状态管理
- 项目CRUD操作状态
- 项目统计信息缓存

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/projects.js` → getProjects() (获取项目列表函数), getProject() (获取单个项目函数), createProject() (创建项目函数), updateProject() (更新项目函数), deleteProject() (删除项目函数), getProjectStats() (获取项目统计函数)
- `src/utils/helpers.js` → formatDate() (日期格式化函数), deepClone() (深拷贝函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useProjectStore` (项目状态Store)
- `projects` (项目列表)
- `currentProject` (当前项目详情)
- `loading` (加载状态)
- `fetchProjects()` (获取项目列表)
- `createProject()` (创建项目)
- `updateProject()` (更新项目)
- `deleteProject()` (删除项目)
- `getProjectStats()` (获取项目统计)

#### src/stores/material.js

**代码文件功能说明**: 
- 物料数据状态管理
- 物料类型、模板、项目物料状态
- 计价方式和参数管理

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/materials.js` → getMaterialTypes() (获取物料类型函数), createMaterialType() (创建物料类型函数), getMaterialTemplates() (获取物料模板函数), createMaterialTemplate() (创建物料模板函数), getProjectMaterials() (获取项目物料函数), createProjectMaterial() (创建项目物料函数), updateProjectMaterial() (更新项目物料函数), deleteProjectMaterial() (删除项目物料函数)
- `src/utils/constants.js` → PRICING_METHODS (计价方式枚举), UNITS (单位枚举)
- `src/composables/useCalculation.js` → calculateMaterialCost() (计算物料成本函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useMaterialStore` (物料状态Store)
- `materialTypes` (物料类型列表)
- `materialTemplates` (物料模板列表)
- `projectMaterials` (项目物料列表)
- `fetchMaterialTypes()` (获取物料类型)
- `createMaterialTemplate()` (创建物料模板)
- `addProjectMaterial()` (添加项目物料)
- `calculateUsage()` (计算物料用量)

#### src/stores/room.js

**代码文件功能说明**: 
- 房间数据状态管理
- 房间类型、模板、项目房间状态
- 房间物料清单管理

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/rooms.js` → getRoomTypes() (获取房间类型函数), createRoomType() (创建房间类型函数), getRoomTemplates() (获取房间模板函数), createRoomTemplate() (创建房间模板函数), getProjectRooms() (获取项目房间函数), createProjectRoom() (创建项目房间函数), updateProjectRoom() (更新项目房间函数), deleteProjectRoom() (删除项目房间函数)
- `src/stores/material.js` → useMaterialStore (物料状态Store函数)
- `src/composables/useCalculation.js` → calculateRoomPrice() (计算房间价格函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useRoomStore` (房间状态Store)
- `roomTypes` (房间类型列表)
- `roomTemplates` (房间模板列表)
- `projectRooms` (项目房间列表)
- `fetchRoomTypes()` (获取房间类型)
- `createRoomTemplate()` (创建房间模板)
- `addProjectRoom()` (添加项目房间)
- `calculateRoomTotal()` (计算房间总价)

#### src/stores/quote.js

**代码文件功能说明**: 
- 报价数据状态管理
- 报价方案CRUD操作
- 报价计算和Excel导出状态

**代码文件依赖的需要格外安装的库**: 
```json
{
  "pinia": "^2.1.6"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/quotes.js` → getQuotes() (获取报价列表函数), createQuote() (创建报价函数), updateQuote() (更新报价函数), deleteQuote() (删除报价函数), copyQuote() (复制报价函数), exportQuoteExcel() (导出Excel函数)
- `src/utils/excel.js` → exportToExcel() (导出Excel函数)
- `src/composables/useCalculation.js` → calculateQuoteTotal() (计算报价总计函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useQuoteStore` (报价状态Store)
- `quotes` (报价列表)
- `currentQuote` (当前报价详情)
- `fetchQuotes()` (获取报价列表)
- `createQuote()` (创建报价)
- `copyQuote()` (复制报价)
- `applyDiscount()` (应用折扣)
- `exportExcel()` (导出Excel)

### 3.4 API接口层

#### src/api/index.js

**代码文件功能说明**: 
- API接口统一配置
- Axios实例创建和拦截器
- 请求响应处理

**代码文件依赖的需要格外安装的库**: 
```json
{
  "axios": "^1.4.0"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/helpers.js` → handleApiError() (API错误处理函数)
- `src/stores/app.js` → useAppStore (应用状态Store函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `apiClient` (Axios实例)
- `request()` (统一请求方法)
- `get()`, `post()`, `put()`, `delete()` (HTTP方法)

#### src/api/projects.js

**代码文件功能说明**: 
- 项目相关API接口
- 项目CRUD操作封装
- 项目统计信息接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/index.js` → apiClient (Axios实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getProjects()` (获取项目列表)
- `getProject()` (获取单个项目)
- `createProject()` (创建项目)
- `updateProject()` (更新项目)
- `deleteProject()` (删除项目)
- `getProjectStats()` (获取项目统计)

#### src/api/materials.js

**代码文件功能说明**: 
- 物料相关API接口
- 物料类型、模板、项目物料接口
- 物料计算相关接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/index.js` → apiClient (Axios实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getMaterialTypes()` (获取物料类型)
- `createMaterialType()` (创建物料类型)
- `getMaterialTemplates()` (获取物料模板)
- `createMaterialTemplate()` (创建物料模板)
- `getProjectMaterials()` (获取项目物料)
- `createProjectMaterial()` (创建项目物料)
- `updateProjectMaterial()` (更新项目物料)
- `deleteProjectMaterial()` (删除项目物料)

#### src/api/rooms.js

**代码文件功能说明**: 
- 房间相关API接口
- 房间类型、模板、项目房间接口
- 房间物料关联接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/index.js` → apiClient (Axios实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getRoomTypes()` (获取房间类型)
- `createRoomType()` (创建房间类型)
- `getRoomTemplates()` (获取房间模板)
- `createRoomTemplate()` (创建房间模板)
- `getProjectRooms()` (获取项目房间)
- `createProjectRoom()` (创建项目房间)
- `updateProjectRoom()` (更新项目房间)
- `deleteProjectRoom()` (删除项目房间)

#### src/api/quotes.js

**代码文件功能说明**: 
- 报价相关API接口
- 报价CRUD操作接口
- Excel导出接口

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/index.js` → apiClient (Axios实例)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `getQuotes()` (获取报价列表)
- `createQuote()` (创建报价)
- `updateQuote()` (更新报价)
- `deleteQuote()` (删除报价)
- `copyQuote()` (复制报价)
- `exportQuoteExcel()` (导出Excel)

### 3.5 组合式函数(Composables)

#### src/composables/useApi.js

**代码文件功能说明**: 
- API调用的通用组合式函数
- 加载状态和错误处理
- 请求缓存和重试机制

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue": "^3.3.4"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/api/index.js` → apiClient (Axios实例)
- `src/composables/useLoading.js` → useLoading (加载状态管理函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useApi()` (API调用组合函数)
- `loading` (加载状态)
- `error` (错误信息)
- `execute()` (执行API请求)
- `refresh()` (刷新数据)

#### src/composables/useConfirm.js

**代码文件功能说明**: 
- 确认对话框的组合式函数
- 统一的确认交互处理
- 支持自定义确认文案

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue": "^3.3.4",
  "element-plus": "^2.3.8"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useConfirm()` (确认对话框组合函数)
- `showConfirm()` (显示确认对话框)
- `confirmDelete()` (删除确认)
- `confirmSave()` (保存确认)

#### src/composables/useCalculation.js

**代码文件功能说明**: 
- 计算相关的组合式函数
- 物料用量计算
- 房间和项目价格计算

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue": "^3.3.4",
  "lodash": "^4.17.21"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/constants.js` → PRICING_METHODS (计价方式枚举常量)
- `src/utils/helpers.js` → safeCalculate() (安全计算函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useCalculation()` (计算组合函数)
- `calculateMaterialCost()` (计算物料成本)
- `calculateRoomPrice()` (计算房间价格)
- `calculateProjectTotal()` (计算项目总价)
- `applyFormula()` (应用计算公式)

#### src/composables/useValidation.js

**代码文件功能说明**: 
- 表单验证的组合式函数
- 业务规则验证
- 实时验证反馈

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vue": "^3.3.4"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/validators.js` → 所有验证函数 (isRequired, isEmail, isNumber等验证器)
- `src/stores/project.js` → useProjectStore (项目状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `useValidation()` (验证组合函数)
- `validateForm()` (表单验证)
- `validateName()` (名称验证)
- `validatePrice()` (价格验证)
- `checkNameUnique()` (检查名称唯一性)

### 3.6 工具类

#### src/utils/constants.js

**代码文件功能说明**: 
- 前端应用常量定义
- 枚举值和配置项
- UI相关常量

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `PRICING_METHODS` (计价方式枚举)
- `UNITS` (单位枚举)
- `ROOM_TYPES` (房间类型枚举)
- `ICON_SIZES` (图标尺寸配置)
- `VALIDATION_RULES` (验证规则)
- `DEFAULT_COLORS` (默认颜色配置)
- `API_ENDPOINTS` (API端点常量)

#### src/utils/helpers.js

**代码文件功能说明**: 
- 通用辅助函数库
- 数据格式化和转换
- 常用算法和工具函数

**代码文件依赖的需要格外安装的库**: 
```json
{
  "dayjs": "^1.11.9",
  "lodash": "^4.17.21"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `formatDate()` (日期格式化)
- `formatCurrency()` (货币格式化)
- `formatNumber()` (数字格式化)
- `deepClone()` (深拷贝)
- `debounce()` (防抖函数)
- `throttle()` (节流函数)
- `generateId()` (生成唯一ID)
- `handleApiError()` (API错误处理)
- `safeCalculate()` (安全计算)

#### src/utils/formatters.js

**代码文件功能说明**: 
- 数据格式化专用工具
- 多种格式化方式支持
- 国际化格式处理

**代码文件依赖的需要格外安装的库**: 
```json
{
  "dayjs": "^1.11.9"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/constants.js` → UNITS (单位枚举常量), PRICING_METHODS (计价方式枚举常量)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `formatPrice()` (价格格式化)
- `formatQuantity()` (数量格式化)
- `formatUnit()` (单位格式化)
- `formatDateTime()` (日期时间格式化)
- `formatUsage()` (用量格式化)
- `formatProjectStats()` (项目统计格式化)

#### src/utils/validators.js

**代码文件功能说明**: 
- 表单验证函数集合
- 业务规则验证
- 数据有效性检查

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/constants.js` → VALIDATION_RULES (验证规则常量)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `validateRequired()` (必填验证)
- `validateName()` (名称验证)
- `validatePrice()` (价格验证)
- `validateNumber()` (数字验证)
- `validateEmail()` (邮箱验证)
- `validatePhone()` (手机验证)
- `validateFormula()` (公式验证)

#### src/utils/excel.js

**代码文件功能说明**: 
- Excel文件处理工具
- 报价表格式化和导出
- 支持多种Excel模板

**代码文件依赖的需要格外安装的库**: 
```json
{
  "xlsx": "^0.18.5"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/formatters.js` → formatPrice() (价格格式化函数), formatDate() (日期格式化函数)
- `src/utils/helpers.js` → deepClone() (深拷贝函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `exportToExcel()` (导出Excel)
- `createWorkbook()` (创建工作簿)
- `formatQuoteSheet()` (格式化报价表)
- `addQuoteData()` (添加报价数据)
- `downloadExcel()` (下载Excel文件)

#### src/utils/storage.js

**代码文件功能说明**: 
- 本地存储工具类
- localStorage和sessionStorage封装
- 数据持久化处理

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `setItem()` (设置存储项)
- `getItem()` (获取存储项)
- `removeItem()` (删除存储项)
- `clear()` (清空存储)
- `setObject()` (存储对象)
- `getObject()` (获取对象)

### 3.7 视图组件

#### src/views/Home.vue

**代码文件功能说明**: 
- 应用首页视图
- 项目概览和快速导航
- 最近项目展示

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/project/ProjectList.vue` → ProjectList组件
- `src/components/common/BaseCard.vue` → BaseCard组件
- `src/stores/project.js` → useProjectStore (项目状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（视图组件）

#### src/views/ProjectManagement.vue

**代码文件功能说明**: 
- 项目管理主页面
- 项目列表展示和操作
- 项目创建和删除功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/project/ProjectList.vue` → ProjectList组件
- `src/components/project/ProjectForm.vue` → ProjectForm组件
- `src/stores/project.js` → useProjectStore (项目状态管理)
- `src/composables/useConfirm.js` → useConfirm (确认对话框函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（视图组件）

#### src/views/ProjectDetail.vue

**代码文件功能说明**: 
- 项目详情页面
- 物料、房间、报价三个区域展示
- 项目编辑和操作功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/project/ProjectWorkspace.vue` → ProjectWorkspace组件
- `src/components/material/MaterialList.vue` → MaterialList组件
- `src/components/room/RoomList.vue` → RoomList组件
- `src/components/quote/QuoteList.vue` → QuoteList组件
- `src/stores/project.js` → useProjectStore (项目状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（视图组件）

#### src/views/MaterialTemplate.vue

**代码文件功能说明**: 
- 物料模板管理页面
- 物料类型和模板管理
- 计价方式配置

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/material/MaterialTemplateManager.vue` → MaterialTemplateManager组件
- `src/components/material/MaterialTypeSelector.vue` → MaterialTypeSelector组件
- `src/stores/material.js` → useMaterialStore (材料状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（视图组件）

#### src/views/RoomTemplate.vue

**代码文件功能说明**: 
- 房间模板管理页面
- 房间类型和模板管理
- 房间物料清单配置

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/room/RoomTemplateManager.vue` → RoomTemplateManager组件
- `src/components/room/RoomMaterialEditor.vue` → RoomMaterialEditor组件
- `src/stores/room.js` → useRoomStore (房间状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（视图组件）

### 3.8 布局组件

#### src/components/layout/MainLayout.vue

**代码文件功能说明**: 
- 主要布局容器组件
- 头部、侧边栏、内容区域布局
- 响应式布局适配

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/layout/AppHeader.vue` → AppHeader组件
- `src/components/layout/AppSidebar.vue` → AppSidebar组件
- `src/stores/app.js` → useAppStore (应用状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（组件）

#### src/components/layout/AppHeader.vue

**代码文件功能说明**: 
- 应用顶部标题栏组件
- 标题显示和用户操作
- 主题切换功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/stores/app.js` → useAppStore (应用状态管理)
- `src/components/common/BaseButton.vue` → BaseButton组件

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（组件）

#### src/components/layout/AppSidebar.vue

**代码文件功能说明**: 
- 左侧导航栏组件
- 主导航按钮和动态信息显示
- 项目状态切换

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/stores/app.js` → useAppStore (应用状态管理)
- `src/stores/project.js` → useProjectStore (项目状态管理)
- `src/components/project/ProjectStats.vue` → ProjectStats组件

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- 无（组件）

### 3.9 业务组件

#### src/components/project/ProjectCard.vue

**代码文件功能说明**: 
- 项目卡片组件
- 项目信息展示
- 删除和编辑操作

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/common/BaseCard.vue` → BaseCard组件
- `src/utils/formatters.js` → formatDate() (日期格式化函数), formatPrice() (价格格式化函数)
- `src/composables/useConfirm.js` → useConfirm (确认对话框函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onEdit` (编辑事件)
- `onDelete` (删除事件)

#### src/components/project/ProjectList.vue

**代码文件功能说明**: 
- 项目列表组件
- 卡片式网格布局
- 新建项目卡片

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/project/ProjectCard.vue` → ProjectCard组件
- `src/stores/project.js` → useProjectStore (项目状态管理)
- `src/composables/useLoading.js` → useLoading (加载状态管理函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onCreateProject` (创建项目事件)
- `onSelectProject` (选择项目事件)

#### src/components/material/MaterialIcon.vue

**代码文件功能说明**: 
- 物料图标组件
- 图标式展示物料信息
- 用量统计显示

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/formatters.js` → formatUsage() (用量格式化函数), formatPrice() (价格格式化函数)
- `src/utils/constants.js` → ICON_SIZES (图标尺寸常量)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onEdit` (编辑事件)
- `onDelete` (删除事件)

#### src/components/material/MaterialList.vue

**代码文件功能说明**: 
- 物料列表组件
- 自动换行图标式展示
- 添加物料功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/material/MaterialIcon.vue` → MaterialIcon组件
- `src/components/material/MaterialForm.vue` → MaterialForm组件  
- `src/stores/material.js` → useMaterialStore (材料状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onAddMaterial` (添加物料事件)
- `onEditMaterial` (编辑物料事件)

#### src/components/room/RoomIcon.vue

**代码文件功能说明**: 
- 房间图标组件
- 比物料图标更大的尺寸
- 房间数量和总价显示

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/utils/formatters.js` → formatPrice() (价格格式化函数), formatQuantity() (数量格式化函数)
- `src/utils/constants.js` → ICON_SIZES (图标尺寸常量)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onEdit` (编辑事件)
- `onDelete` (删除事件)

#### src/components/room/RoomList.vue

**代码文件功能说明**: 
- 房间列表组件
- 图标式展示房间信息
- 添加房间功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/room/RoomIcon.vue` → RoomIcon组件
- `src/components/room/RoomForm.vue` → RoomForm组件
- `src/stores/room.js` → useRoomStore (房间状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onAddRoom` (添加房间事件)
- `onEditRoom` (编辑房间事件)

#### src/components/quote/QuoteCard.vue

**代码文件功能说明**: 
- 报价卡片组件
- 报价名称和金额显示
- 操作按钮集成

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/common/BaseCard.vue` → BaseCard组件
- `src/utils/formatters.js` → formatPrice() (价格格式化函数)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onEdit` (编辑事件)
- `onCopy` (复制事件)
- `onDelete` (删除事件)
- `onExport` (导出事件)

#### src/components/quote/QuoteList.vue

**代码文件功能说明**: 
- 报价列表组件
- 多方案报价展示
- 大项/细项层级结构

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/quote/QuoteCard.vue` → QuoteCard组件
- `src/components/quote/QuoteDetailView.vue` → QuoteDetailView组件
- `src/stores/quote.js` → useQuoteStore (报价状态管理)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `onCreateQuote` (创建报价事件)
- `onViewDetail` (查看详情事件)

### 3.10 通用组件

#### src/components/common/BaseDialog.vue

**代码文件功能说明**: 
- 基础对话框组件
- 统一的对话框样式
- 支持自定义内容和操作

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `visible` (显示状态)
- `title` (标题属性)
- `onConfirm` (确认事件)
- `onCancel` (取消事件)

#### src/components/common/ConfirmDialog.vue

**代码文件功能说明**: 
- 确认对话框组件
- 删除确认和保存确认
- 防误操作功能

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/components/common/BaseDialog.vue` → BaseDialog组件

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `showConfirm()` (显示确认对话框)
- `confirmDelete()` (删除确认)
- `confirmSave()` (保存确认)

#### src/components/common/BaseForm.vue

**代码文件功能说明**: 
- 基础表单组件
- 统一的表单样式和验证
- 支持动态表单字段

**代码文件依赖的需要格外安装的库**: 
- 无（Vue SFC）

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/composables/useValidation.js` → useValidation (表单验证组合函数)
- `src/utils/validators.js` → 验证函数 (isRequired, isEmail, isNumber等)

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `formData` (表单数据)
- `validate()` (表单验证)
- `reset()` (重置表单)
- `onSubmit` (提交事件)

## 4. 环境配置文件

### 4.1 package.json

```json
{
  "name": "material-system-frontend",
  "version": "1.0.0",
  "description": "Interior Design Material Price Calculation System Frontend",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "vue": "^3.3.4",
    "vue-router": "^4.2.4",
    "pinia": "^2.1.6",
    "element-plus": "^2.3.8",
    "@element-plus/icons-vue": "^2.1.0",
    "axios": "^1.4.0",
    "xlsx": "^0.18.5",
    "dayjs": "^1.11.9",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.2.3",
    "@vue/test-utils": "^2.4.1",
    "vite": "^4.4.5",
    "vitest": "^0.34.1",
    "sass": "^1.64.1",
    "eslint": "^8.45.0",
    "eslint-plugin-vue": "^9.15.1",
    "prettier": "^3.0.0",
    "@vitest/ui": "^0.34.1",
    "@vitest/coverage-c8": "^0.33.0"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
```

### 4.2 .env.development

```env
# 开发环境配置
VITE_APP_TITLE=物料价格计算软件
VITE_API_BASE_URL=http://localhost:3000/api/v1
VITE_APP_MODE=development

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true

# Excel导出配置
VITE_EXCEL_MAX_ROWS=10000
```

### 4.3 .env.production

```env
# 生产环境配置
VITE_APP_TITLE=物料价格计算软件
VITE_API_BASE_URL=/api/v1
VITE_APP_MODE=production

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=false

# Excel导出配置
VITE_EXCEL_MAX_ROWS=50000
```

## 5. 样式系统

### 5.1 src/assets/styles/main.scss

**代码文件功能说明**: 
- 全局样式入口文件
- 重置样式和基础样式
- 响应式布局样式

**代码文件依赖的需要格外安装的库**: 
```json
{
  "sass": "^1.64.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `src/assets/styles/variables.scss` → CSS变量
- `src/assets/styles/components.scss` → 组件样式

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- CSS类和样式规则

### 5.2 src/assets/styles/variables.scss

**代码文件功能说明**: 
- CSS变量定义文件
- 颜色、尺寸、字体等变量
- 主题配置变量

**代码文件依赖的需要格外安装的库**: 
- 无

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- 无

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `$primary-color` (主色调)
- `$success-color` (成功色)
- `$warning-color` (警告色)
- `$error-color` (错误色)
- `$font-family` (字体族)
- `$border-radius` (圆角半径)

## 6. 测试配置

### 6.1 vitest.config.js

**代码文件功能说明**: 
- Vitest测试框架配置
- 测试环境设置
- 覆盖率报告配置

**代码文件依赖的需要格外安装的库**: 
```json
{
  "vitest": "^0.34.1",
  "@vitest/ui": "^0.34.1",
  "@vue/test-utils": "^2.4.1"
}
```

**代码文件需要依赖的其他代码文件及其中的函数、变量**: 
- `vite.config.js` → Vite配置

**当前代码文件可以提供给其他代码文件引用的变量、函数**: 
- `config` (测试配置对象)

## 7. 核心功能实现要点

### 7.1 响应式数据管理
- 使用Pinia进行状态管理
- 响应式数据更新和计算
- 组件间数据共享

### 7.2 实时计算功能
- 物料用量自动累加计算
- 房间价格实时更新
- 项目总价动态计算
- 折扣系数实时应用

### 7.3 表单验证体系
- 名称重复性检查
- 数据有效性验证
- 实时验证反馈
- 统一错误处理

### 7.4 用户交互优化
- 确认对话框防误操作
- 加载状态反馈
- 操作成功提示
- 错误信息展示

### 7.5 Excel导出功能
- 多格式报价表导出
- 自定义模板支持
- 大数据量处理
- 浏览器兼容性

### 7.6 本地存储机制
- 用户偏好设置保存
- 临时数据缓存
- 离线数据支持
- 数据同步处理

## 8. 部署说明

### 8.1 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test
```

### 8.2 生产环境构建

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 8.3 代码质量检查

```bash
# ESLint代码检查
npm run lint

# Prettier代码格式化
npm run format

# 测试覆盖率报告
npm run test:coverage
```
