# 室内建筑设计师物料价格计算软件 - 产品原型文档

## 1. 产品概述

### 1.1 产品定位
面向室内建筑设计师的专业物料价格计算与报价生成工具，帮助设计师高效管理项目物料成本，生成专业报价报表。

### 1.2 目标用户
- 室内建筑设计师
- 装修公司项目经理
- 工程造价人员

### 1.3 核心价值
- 提高物料成本计算效率
- 标准化报价流程
- 减少人工计算错误
- 支持多方案报价对比

## 2. 技术架构

### 2.1 技术栈
- **前端框架**: Vue.js 3 + Vite
- **UI组件库**: Element Plus
- **数据库**: SQLite3 (Node.js sqlite3模块)
- **文件处理**: ExcelJS (xlsx)
- **运行环境**: Web浏览器
- **部署平台**: Windows 10+ / macOS

### 2.2 架构特点
- 单页面应用(SPA)
- 本地数据存储
- 离线可用
- 跨平台兼容

## 3. 界面布局设计

### 3.1 整体布局
```
┌─────────────────────────────────────────────────────┐
│                    顶部标题栏                        │
├──────────────┬──────────────────────────────────────┤
│              │                                      │
│   左侧导航栏   │           右侧操作区域                │
│              │                                      │
│              │                                      │
│              │                                      │
└──────────────┴──────────────────────────────────────┘
```

### 3.2 左侧导航栏结构

#### 3.2.1 上部区域 - 主导航按钮
- **项目管理** - 项目列表与管理
- **物料模板** - 物料类型与模板管理  
- **房间模板** - 房间类型与模板管理

#### 3.2.2 下部区域 - 动态信息显示

**状态1: 未选择项目时**
- 显示最近项目列表
- 按修改时间排序
- 快速进入历史项目

**状态2: 已选择项目时**
- 项目基本信息
- 统计数据概览
- 实时报价信息

## 4. 功能模块设计

### 4.1 项目管理模块

#### 4.1.1 项目列表页面
**布局**: 卡片式网格布局

**项目卡片内容**:
- 项目名称
- 创建时间
- 最新修改时间
- 物料种类数量
- 房间种类数量
- 最终报价金额
- 删除操作按钮

**特殊卡片**:
- 首位固定"新建项目"卡片
- 显示文案: "开始你的下一个梦想"
- 无删除按钮

**交互逻辑**:
- 点击项目卡片 → 进入项目编辑
- 点击删除按钮 → 确认弹窗 → 删除项目
- 按创建时间排序显示

#### 4.1.2 项目操作页面

**页面结构**:
```
┌─────────────────────────────────────────────────────┐
│                   物料列表区域                       │
├─────────────────────────────────────────────────────┤
│                   房间列表区域                       │
├─────────────────────────────────────────────────────┤
│                   报价列表区域                       │
└─────────────────────────────────────────────────────┘
```

**物料列表区域**:
- 图标式展示，自动换行
- 显示物料名称 + 用量统计（如"×3件"、"×20平方米"）
- 用量数据来源：项目中所有房间该物料的单位累加
- 首位"+"图标用于添加新物料
- 点击图标进入物料编辑页面
- 右上角删除按钮（需用户确认）

**物料编辑功能**:
- 从物料模板中选择并修改单价
- 调整计价方式参数
- 设置物料在项目中的名称（项目内不重复）
- 支持将物料保存为公共模板（全局名称不重复）

**房间列表区域**:
- 图标式展示，比物料图标更大
- 显示房间名称 + 数量统计（如"×2间"）
- 显示房间总报价（所有物料统计报价）
- 房间数量由用户添加时设置
- 首位"+"图标用于添加新房间
- 点击图标进入房间编辑页面

**报价列表区域**:
- 支持多方案报价
- 大项/细项层级展示结构：
  - 大项：按物料种类显示（用量 × 单价 = 总价）
  - 细项：展开显示来自不同房间的该物料详情
- 支持打折系数设置生成新报价
- 报价方案复制功能（可选择已有报价作为模板）
- 报价方案名称不可重复
- 显示报价名称 + 最终价位（如"标准方案（￥50,000）"）
- 导出Excel功能
- 实时数据计算和更新

### 4.2 物料模板模块

#### 4.2.1 数据模型层次
```
物料类型 (Material Type)
├── 物料模板 (Material Template)
└── 项目物料 (Project Material)
```

#### 4.2.2 物料类型管理

**计价方式定义**:
1. **按长度计价**
   - 单位选择: 分米/厘米/毫米
   - 参数: 长度 × 单价
   
2. **按面积计价**
   - 单位: 平方米
   - 参数: 面积 × 单价
   
3. **按重量计价**
   - 单位选择: 克/千克
   - 参数: 重量 × 单价
   
4. **按体积计价**
   - 单位: 立方米
   - 参数: 体积 × 单价
   
5. **按数量计价**
   - 单位: 件/个/套等
   - 参数: 数量 × 单价

**界面布局**:
- 图标式类型选择器
- 固定"全部物料"选项
- 首位"+"图标添加新类型
- 类型筛选功能

#### 4.2.3 物料模板管理
- 基于物料类型创建模板
- 设置默认参数和单价
- 支持模板复制和修改
- 模板删除不影响已创建物料（数据复制方式，非指针引用）

**界面布局**:
- 按物料类型分行显示模板
- 同类型模板在一行内展示，不够换行
- 每行末尾有添加该类型物料的按钮
- 模板图标右上角有删除按钮（需确认）
- 点击模板图标可修改模板信息

### 4.3 房间模板模块

#### 4.3.1 数据模型层次
```
房间类型 (Room Type)
├── 房间模板 (Room Template)
└── 项目房间 (Project Room)
```

#### 4.3.2 房间模板特性
- 预设物料清单
- 自定义计算公式
- 支持复杂面积计算
- 加项/减项灵活配置

**房间模板编辑功能**:
- 可添加任意物料到房间模板
- 为每种物料设置单价和数量
- 支持长度/面积单位物料的多种计算方式
- 公式支持加项和减项的灵活组合
- 界面布局与物料模板完全相同

**计算公式示例**:
```
基础计算: 面积 = 长 × 宽
复杂计算: 墙纸面积 = 墙面长 × 墙面高 - 门宽 × 门高 - 窗宽 × 窗高
```

## 5. 用户交互流程

### 5.1 新建项目流程
```
点击"新建项目" → 输入项目信息 → 创建成功 → 进入项目编辑
```

### 5.2 添加物料流程
```
项目页面 → 点击"+"添加物料 → 选择物料模板 → 设置参数 → 保存物料
```

### 5.3 添加房间流程
```
项目页面 → 点击"+"添加房间 → 选择房间模板 → 设置房间数量 → 配置物料清单 → 保存房间
```

### 5.4 生成报价流程
```
完善物料清单 → 点击"+"新建报价 → 设置报价参数 → 生成报价表 → 导出Excel
```

### 5.5 导航状态切换流程
```
点击项目管理 → 退出当前项目 → 左侧显示历史项目列表
点击具体项目 → 进入项目编辑 → 左侧显示项目统计信息
```

## 6. 数据管理

### 6.1 数据持久化
- SQLite本地数据库
- 支持数据导入/导出
- 自动备份机制

### 6.2 数据关系
- 项目 ← 物料/房间
- 模板 → 项目实例 (复制关系)
- 删除模板不影响已创建实例

### 6.3 时间戳管理
- 创建时间记录
- 修改时间更新
- 界面显示时间信息
- 项目列表按修改时间排序（最近修改优先）

### 6.4 数据验证规则
- 项目内物料名称不可重复
- 公共模板名称全局不可重复
- 报价方案名称项目内不可重复
- 物料类型名称全局不可重复
- 房间类型名称全局不可重复

## 7. 性能与体验

### 7.1 响应式设计
- 适配不同屏幕尺寸
- 移动端友好界面

### 7.2 操作体验
- 确认弹窗防误操作（删除项目、物料、房间、报价等）
- 实时数据计算（用量统计、报价计算）
- 快速模板复制（物料模板、房间模板、报价方案）
- 智能排序（项目按修改时间、物料按创建时间）

## 8. 补充功能要点

### 8.1 界面交互细节
- 所有删除操作都需要用户确认弹窗
- 图标大小区分：房间图标 > 物料图标
- 名称重复检查：保存时实时验证
- 数据复制机制：模板删除不影响已创建实例

### 8.2 计算逻辑
- 物料用量 = 所有房间中该物料的累加
- 房间报价 = 房间内所有物料的价格总和
- 项目报价 = 所有物料的最终计算结果
- 支持打折系数调整最终报价

### 8.3 模板系统
- 三层数据结构：类型 → 模板 → 项目实例
- 数据复制而非引用，确保独立性
- 支持从项目物料反向保存为公共模板
